<template>
  <section id="features-section" class="features-section py-5" role="main">
    <div class="container">
      <!-- Section Header -->
      <header class="text-center mb-5">
        <h2 class="section-title display-5 font-weight-bold text-dark mb-3">
          {{ featuresData.title }}
        </h2>
        <p class="section-subtitle lead text-muted mb-4">
          {{ featuresData.subtitle }}
        </p>
        <div class="title-divider mx-auto"></div>
      </header>
      
      <!-- Features Grid -->
      <div class="row g-4">
        <div 
          v-for="(feature, index) in featuresData.items" 
          :key="index"
          class="col-lg-4 col-md-6"
        >
          <article class="feature-card h-100">
            <div class="feature-icon-wrapper">
              <div class="feature-icon">
                <fa :icon="getFeatureIcon(feature.icon)" />
              </div>
            </div>
            
            <div class="feature-content">
              <h3 class="feature-title h5 font-weight-bold mb-3">
                {{ feature.title }}
              </h3>
              
              <p class="feature-description text-muted mb-0">
                {{ feature.description }}
              </p>
            </div>
          </article>
        </div>
      </div>
      
      <!-- Call to Action -->
      <div class="text-center mt-5">
        <button
          @click="scrollToEditor"
          class="btn btn-primary btn-lg"
          :aria-label="ctaText"
        >
          <fa :icon="['fas', 'rocket']" class="me-2" />
          {{ ctaText }}
        </button>
      </div>
    </div>
  </section>
</template>

<script>
import { mapGetters } from 'vuex'

export default {
  name: 'FeaturesSection',
  
  computed: {
    ...mapGetters(['currentUIData']),
    
    featuresData() {
      return this.currentUIData.sections?.features || {
        title: '強大功能',
        subtitle: '一切你需要的卡片製作功能',
        items: [
          {
            title: '全類型支持',
            description: '支持怪獸卡、魔法卡、陷阱卡及其所有子類型。',
            icon: 'cards'
          },
          {
            title: '多語言支持',
            description: '支持中文、日文、英文三種語言。',
            icon: 'globe'
          },
          {
            title: '高質量輸出',
            description: '使用 Canvas 技術生成高分辨率卡片圖片。',
            icon: 'download'
          }
        ]
      }
    },
    
    ctaText() {
      return this.currentUIData.sections?.hero?.cta_button || '開始製作'
    }
  },
  
  methods: {
    /**
     * 獲取功能圖標
     * @param {string} iconName - 圖標名稱
     * @returns {Array} Font Awesome 圖標數組
     */
    getFeatureIcon(iconName) {
      const iconMap = {
        cards: ['fas', 'layer-group'],
        globe: ['fas', 'globe'],
        download: ['fas', 'download'],
        eye: ['fas', 'eye'],
        magic: ['fas', 'magic'],
        heart: ['fas', 'heart']
      }
      
      return iconMap[iconName] || ['fas', 'star']
    },
    
    /**
     * 滾動到編輯器區域
     */
    scrollToEditor() {
      const editorElement = document.getElementById('card-editor')
      if (editorElement) {
        editorElement.scrollIntoView({ 
          behavior: 'smooth',
          block: 'start'
        })
      }
    }
  }
}
</script>

<style scoped>
.features-section {
  background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
  position: relative;
}

.features-section::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-image: 
    radial-gradient(circle at 25% 25%, rgba(0, 123, 255, 0.05) 0%, transparent 50%),
    radial-gradient(circle at 75% 75%, rgba(108, 117, 125, 0.05) 0%, transparent 50%);
  pointer-events: none;
}

.container {
  position: relative;
  z-index: 1;
}

.section-title {
  color: #2c3e50;
  font-weight: 700;
  margin-bottom: 1rem;
}

.section-subtitle {
  color: #6c757d;
  font-size: 1.1rem;
  max-width: 600px;
  margin: 0 auto;
}

.title-divider {
  width: 80px;
  height: 4px;
  background: linear-gradient(45deg, #007bff, #6610f2);
  border-radius: 2px;
}

.feature-card {
  background: white;
  border-radius: 15px;
  padding: 2rem;
  text-align: center;
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.08);
  transition: all 0.3s ease;
  border: 1px solid rgba(0, 0, 0, 0.05);
  position: relative;
  overflow: hidden;
}

.feature-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  background: linear-gradient(45deg, #007bff, #6610f2);
  transform: scaleX(0);
  transition: transform 0.3s ease;
}

.feature-card:hover {
  transform: translateY(-10px);
  box-shadow: 0 15px 35px rgba(0, 0, 0, 0.15);
}

.feature-card:hover::before {
  transform: scaleX(1);
}

.feature-icon-wrapper {
  margin-bottom: 1.5rem;
}

.feature-icon {
  width: 80px;
  height: 80px;
  background: linear-gradient(135deg, #007bff, #6610f2);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 0 auto;
  color: white;
  font-size: 2rem;
  transition: all 0.3s ease;
  position: relative;
}

.feature-icon::before {
  content: '';
  position: absolute;
  top: -5px;
  left: -5px;
  right: -5px;
  bottom: -5px;
  background: linear-gradient(135deg, #007bff, #6610f2);
  border-radius: 50%;
  opacity: 0;
  transition: opacity 0.3s ease;
  z-index: -1;
}

.feature-card:hover .feature-icon {
  transform: scale(1.1);
}

.feature-card:hover .feature-icon::before {
  opacity: 0.2;
}

.feature-title {
  color: #2c3e50;
  font-weight: 600;
  margin-bottom: 1rem;
}

.feature-description {
  color: #6c757d;
  line-height: 1.6;
  font-size: 0.95rem;
}

.btn-primary {
  background: linear-gradient(45deg, #007bff, #0056b3);
  border: none;
  padding: 1rem 2rem;
  font-weight: 600;
  border-radius: 50px;
  box-shadow: 0 4px 15px rgba(0, 123, 255, 0.4);
  transition: all 0.3s ease;
}

.btn-primary:hover {
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(0, 123, 255, 0.6);
}

/* Animation on scroll */
.feature-card {
  opacity: 0;
  transform: translateY(30px);
  animation: fadeInUp 0.6s ease forwards;
}

.feature-card:nth-child(1) { animation-delay: 0.1s; }
.feature-card:nth-child(2) { animation-delay: 0.2s; }
.feature-card:nth-child(3) { animation-delay: 0.3s; }
.feature-card:nth-child(4) { animation-delay: 0.4s; }
.feature-card:nth-child(5) { animation-delay: 0.5s; }
.feature-card:nth-child(6) { animation-delay: 0.6s; }

@keyframes fadeInUp {
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Responsive design */
@media (max-width: 768px) {
  .feature-card {
    padding: 1.5rem;
  }
  
  .feature-icon {
    width: 60px;
    height: 60px;
    font-size: 1.5rem;
  }
  
  .section-title {
    font-size: 2rem;
  }
  
  .btn-primary {
    padding: 0.75rem 1.5rem;
  }
}

@media (max-width: 576px) {
  .feature-card {
    padding: 1.25rem;
  }
  
  .section-title {
    font-size: 1.75rem;
  }
}

/* Accessibility */
@media (prefers-reduced-motion: reduce) {
  .feature-card,
  .feature-icon,
  .btn-primary {
    animation: none;
    transition: none;
  }
  
  .feature-card:hover {
    transform: none;
  }
}

/* High contrast mode */
@media (prefers-contrast: high) {
  .feature-card {
    border: 2px solid #000;
    background: #fff;
  }
  
  .feature-icon {
    background: #000;
    color: #fff;
  }
  
  .feature-title {
    color: #000;
  }
  
  .feature-description {
    color: #333;
  }
}

/* Focus styles for accessibility */
.feature-card:focus-within {
  outline: 2px solid #007bff;
  outline-offset: 2px;
}

.btn-primary:focus {
  outline: 2px solid #007bff;
  outline-offset: 2px;
}
</style>
