<template>
  <div class="lazy-image-container" :style="containerStyle">
    <img
      v-if="loaded"
      :src="src"
      :alt="alt"
      :class="imageClass"
      :style="imageStyle"
      @load="onLoad"
      @error="onError"
    />
    <div
      v-else-if="loading"
      class="lazy-image-placeholder"
      :style="placeholderStyle"
    >
      <div class="lazy-image-spinner">
        <div class="spinner"></div>
      </div>
    </div>
    <div
      v-else-if="error"
      class="lazy-image-error"
      :style="placeholderStyle"
    >
      <fa :icon="['fas', 'image']" class="error-icon" />
      <span class="error-text">{{ errorText || 'Failed to load image' }}</span>
    </div>
  </div>
</template>

<script>
export default {
  name: 'LazyImage',
  props: {
    src: {
      type: String,
      required: true
    },
    alt: {
      type: String,
      default: ''
    },
    width: {
      type: [String, Number],
      default: null
    },
    height: {
      type: [String, Number],
      default: null
    },
    imageClass: {
      type: String,
      default: ''
    },
    errorText: {
      type: String,
      default: null
    },
    lazy: {
      type: Boolean,
      default: true
    }
  },
  data() {
    return {
      loaded: false,
      loading: false,
      error: false,
      observer: null
    }
  },
  computed: {
    containerStyle() {
      const style = {}
      if (this.width) style.width = typeof this.width === 'number' ? `${this.width}px` : this.width
      if (this.height) style.height = typeof this.height === 'number' ? `${this.height}px` : this.height
      return style
    },
    imageStyle() {
      return {
        width: '100%',
        height: '100%',
        objectFit: 'cover'
      }
    },
    placeholderStyle() {
      return {
        width: '100%',
        height: '100%',
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'center',
        backgroundColor: '#f8f9fa',
        border: '1px solid #dee2e6',
        borderRadius: '4px'
      }
    }
  },
  mounted() {
    if (this.lazy && 'IntersectionObserver' in window) {
      this.setupIntersectionObserver()
    } else {
      this.loadImage()
    }
  },
  beforeDestroy() {
    if (this.observer) {
      this.observer.disconnect()
    }
  },
  methods: {
    setupIntersectionObserver() {
      this.observer = new IntersectionObserver(
        (entries) => {
          entries.forEach((entry) => {
            if (entry.isIntersecting) {
              this.loadImage()
              this.observer.unobserve(entry.target)
            }
          })
        },
        {
          rootMargin: '50px'
        }
      )
      this.observer.observe(this.$el)
    },
    loadImage() {
      if (this.loaded || this.loading) return
      
      this.loading = true
      this.error = false
      
      const img = new Image()
      img.onload = () => {
        this.loaded = true
        this.loading = false
        this.$emit('load')
      }
      img.onerror = () => {
        this.error = true
        this.loading = false
        this.$emit('error')
      }
      img.src = this.src
    },
    onLoad() {
      this.$emit('load')
    },
    onError() {
      this.error = true
      this.$emit('error')
    }
  }
}
</script>

<style scoped>
.lazy-image-container {
  position: relative;
  overflow: hidden;
}

.lazy-image-placeholder,
.lazy-image-error {
  background-color: #f8f9fa;
  color: #6c757d;
}

.lazy-image-spinner {
  display: flex;
  align-items: center;
  justify-content: center;
}

.spinner {
  width: 20px;
  height: 20px;
  border: 2px solid #f3f3f3;
  border-top: 2px solid #007bff;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.error-icon {
  font-size: 24px;
  color: #dc3545;
  margin-bottom: 8px;
}

.error-text {
  font-size: 12px;
  text-align: center;
}

.lazy-image-error {
  flex-direction: column;
}
</style>
