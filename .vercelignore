# Dependencies
node_modules

# Build outputs
.nuxt
dist

# Development files
.env
.env.local
.env.*.local

# IDE files
.vscode
.idea
*.swp
*.swo

# OS files
.DS_Store
Thumbs.db

# Logs
*.log
npm-debug.log*
yarn-debug.log*
yarn-error.log*
pnpm-debug.log*

# Lock files (keep only package-lock.json for npm)
pnpm-lock.yaml
yarn.lock

# Coverage directory used by tools like istanbul
coverage

# nyc test coverage
.nyc_output

# Temporary folders
tmp
temp
