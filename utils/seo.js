/**
 * Simple SEO utilities for Yu-Gi-Oh! Card Maker
 * Minimal, efficient implementation for canonical URLs and hreflang tags
 */

const BASE_URL = 'https://yugiohcardmaker.org'
const LANGUAGES = ['en', 'zh', 'ja', 'de', 'fr', 'ko', 'pt', 'es', 'el', 'th', 'ru', 'vi']

/**
 * Generate canonical URL for current page
 * @param {string} locale - Current locale (e.g., 'en', 'zh')
 * @param {string} path - Current route path (e.g., '/', '/privacy')
 * @returns {string} Canonical URL
 */
export function getCanonicalUrl(locale, path = '/') {
  // Clean path - remove locale prefix if present
  let cleanPath = path
  if (locale !== 'en' && path.startsWith(`/${locale}`)) {
    cleanPath = path.replace(`/${locale}`, '') || '/'
  }
  
  // Generate canonical URL
  if (locale === 'en') {
    return cleanPath === '/' ? `${BASE_URL}/` : `${BASE_URL}${cleanPath}`
  } else {
    return cleanPath === '/' ? `${BASE_URL}/${locale}/` : `${BASE_URL}/${locale}${cleanPath}`
  }
}

/**
 * Generate hreflang links for all languages
 * @param {string} path - Current route path (e.g., '/', '/privacy')
 * @returns {Array} Array of hreflang link objects
 */
export function getHreflangLinks(path = '/') {
  // Clean path - remove any locale prefix
  let cleanPath = path
  for (const lang of LANGUAGES) {
    if (path.startsWith(`/${lang}/`) || path === `/${lang}`) {
      cleanPath = path.replace(`/${lang}`, '') || '/'
      break
    }
  }
  
  const links = []
  
  // Add hreflang for each language
  LANGUAGES.forEach(lang => {
    const href = lang === 'en' 
      ? (cleanPath === '/' ? `${BASE_URL}/` : `${BASE_URL}${cleanPath}`)
      : (cleanPath === '/' ? `${BASE_URL}/${lang}/` : `${BASE_URL}/${lang}${cleanPath}`)
    
    links.push({
      hid: `hreflang-${lang}`,
      rel: 'alternate',
      hreflang: lang,
      href
    })
  })
  
  // Add x-default (points to English)
  links.push({
    hid: 'hreflang-x-default',
    rel: 'alternate',
    hreflang: 'x-default',
    href: cleanPath === '/' ? `${BASE_URL}/` : `${BASE_URL}${cleanPath}`
  })
  
  return links
}

/**
 * Generate complete SEO head configuration
 * @param {Object} options - SEO options
 * @param {string} options.title - Page title
 * @param {string} options.description - Page description
 * @param {string} options.locale - Current locale
 * @param {string} options.path - Current route path
 * @param {string} options.robots - Robots directive (default: 'index, follow')
 * @returns {Object} Head configuration object
 */
export function generateSEOHead({ title, description, locale = 'en', path = '/', robots = 'index, follow' }) {
  const canonicalUrl = getCanonicalUrl(locale, path)
  const hreflangLinks = getHreflangLinks(path)
  
  return {
    title,
    htmlAttrs: {
      lang: locale
    },
    meta: [
      { hid: 'description', name: 'description', content: description },
      { hid: 'robots', name: 'robots', content: robots },
      { hid: 'googlebot', name: 'googlebot', content: robots },
      { hid: 'og:title', property: 'og:title', content: title },
      { hid: 'og:description', property: 'og:description', content: description },
      { hid: 'og:url', property: 'og:url', content: canonicalUrl },
      { hid: 'og:type', property: 'og:type', content: 'website' },
      { hid: 'twitter:card', name: 'twitter:card', content: 'summary' },
      { hid: 'twitter:title', name: 'twitter:title', content: title },
      { hid: 'twitter:description', name: 'twitter:description', content: description }
    ],
    link: [
      { hid: 'canonical', rel: 'canonical', href: canonicalUrl },
      ...hreflangLinks
    ]
  }
}
