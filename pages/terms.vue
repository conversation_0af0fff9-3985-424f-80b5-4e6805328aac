<template>
  <div class="terms-page">
    <!-- Navigation Header -->
    <header role="banner">
      <b-navbar type="dark" fixed="top" class="main-navbar" expand="lg">
        <b-navbar-brand href="/" class="navbar-brand-custom">
          <h1 class="brand-title mb-0">
            <span class="brand-main">Yu-Gi-Oh! Card Maker</span>
            <small class="brand-sub d-block">遊戲王卡片製造機</small>
          </h1>
        </b-navbar-brand>
        
        <!-- Language Switcher -->
        <b-navbar-nav class="ml-auto">
          <LanguageSwitcher />
        </b-navbar-nav>
      </b-navbar>
    </header>

    <!-- Main Content -->
    <main class="terms-content">
      <div class="container">
        <div class="row justify-content-center">
          <div class="col-lg-8">
            <div class="content-wrapper">
              <h1 class="page-title">Terms of Service</h1>
              <p class="last-updated">Last updated: {{ new Date().toLocaleDateString() }}</p>

              <section class="terms-section">
                <h2>1. Acceptance of Terms</h2>
                <p>By accessing and using yugiohcardmaker.org ("the Service"), you accept and agree to be bound by the terms and provisions of this agreement. If you do not agree to abide by these terms, please do not use this service.</p>
              </section>

              <section class="terms-section">
                <h2>2. Description of Service</h2>
                <p>yugiohcardmaker.org is a free online tool that allows users to create custom Yu-Gi-Oh! trading card designs for personal, educational, and entertainment purposes. The service includes:</p>
                <ul>
                  <li>Professional card design templates and customization tools</li>
                  <li>Image upload and automatic positioning capabilities</li>
                  <li>Multi-language support (English, Japanese, Chinese)</li>
                  <li>High-resolution card image download functionality</li>
                  <li>Real-time preview and editing features</li>
                  <li>Various card types support (Monster, Spell, Trap, Pendulum, Link, etc.)</li>
                </ul>
              </section>

              <section class="terms-section">
                <h2>3. User Responsibilities</h2>
                <p>Users of this service agree to:</p>
                <ul>
                  <li>Use the service for personal, non-commercial purposes only</li>
                  <li>Not upload or create content that is illegal, harmful, or offensive</li>
                  <li>Respect intellectual property rights of others</li>
                  <li>Not attempt to reverse engineer or exploit the service</li>
                  <li>Not use the service to create counterfeit or fraudulent cards</li>
                </ul>
              </section>

              <section class="terms-section">
                <h2>4. Intellectual Property</h2>
                <p>Important disclaimers regarding intellectual property:</p>
                <ul>
                  <li><strong>Yu-Gi-Oh! Trademark:</strong> Yu-Gi-Oh! is a trademark of Konami Digital Entertainment Co., Ltd. This service is not affiliated with, endorsed by, or sponsored by Konami Digital Entertainment or any of its subsidiaries.</li>
                  <li><strong>Educational Purpose:</strong> This tool is provided for educational, entertainment, and fan creation purposes only</li>
                  <li><strong>User Content:</strong> Users retain ownership of original content they create using our tools, but must respect third-party intellectual property rights</li>
                  <li><strong>Service Code:</strong> The underlying code and design of this service is protected by copyright and owned by yugiohcardmaker.org</li>
                  <li><strong>Fair Use:</strong> This tool is provided under fair use principles for non-commercial, educational, and transformative purposes</li>
                  <li><strong>No Commercial Use:</strong> Cards created using this service should not be used for commercial purposes or sold as official Yu-Gi-Oh! products</li>
                </ul>
              </section>

              <section class="terms-section">
                <h2>5. Disclaimer of Warranties</h2>
                <p>This service is provided "as is" without any warranties, expressed or implied. We do not guarantee:</p>
                <ul>
                  <li>Continuous availability of the service</li>
                  <li>Error-free operation</li>
                  <li>Compatibility with all devices or browsers</li>
                  <li>Preservation of user-created content</li>
                </ul>
              </section>

              <section class="terms-section">
                <h2>6. Limitation of Liability</h2>
                <p>In no event shall Yu-Gi-Oh! Card Maker be liable for any indirect, incidental, special, consequential, or punitive damages, including without limitation, loss of profits, data, use, goodwill, or other intangible losses.</p>
              </section>

              <section class="terms-section">
                <h2>7. User-Generated Content</h2>
                <p>Users are solely responsible for content they create using our service. We reserve the right to remove any content that violates these terms or applicable laws.</p>
              </section>

              <section class="terms-section">
                <h2>8. Service Modifications</h2>
                <p>We reserve the right to modify or discontinue the service at any time without notice. We may also update these terms periodically.</p>
              </section>

              <section class="terms-section">
                <h2>9. Governing Law</h2>
                <p>These terms shall be governed by and construed in accordance with applicable laws, without regard to conflict of law provisions.</p>
              </section>

              <section class="terms-section">
                <h2>10. Contact Information</h2>
                <p>If you have any questions about these Terms of Service, please contact us at:</p>
                <ul>
                  <li><strong>Website:</strong> yugiohcardmaker.org</li>
                  <li><strong>Email:</strong> <EMAIL></li>
                </ul>
                <p>We will respond to inquiries within a reasonable timeframe.</p>
              </section>

              <div class="back-link">
                <nuxt-link to="/" class="btn btn-primary">
                  <fa :icon="['fas', 'arrow-left']" class="me-2" />
                  Back to Card Maker
                </nuxt-link>
              </div>
            </div>
          </div>
        </div>
      </div>
    </main>
  </div>
</template>

<script>
import LanguageSwitcher from '~/components/LanguageSwitcher.vue'
import { generateSEOHead } from '~/utils/seo.js'

export default {
  name: 'TermsPage',

  components: {
    LanguageSwitcher
  },

  head() {
    // Use simplified SEO utility for terms page
    return generateSEOHead({
      title: 'Terms of Service - Yu-Gi-Oh! Card Maker',
      description: 'Terms of Service for yugiohcardmaker.org. Read our terms and conditions for using our Yu-Gi-Oh! card creation service.',
      locale: this.$i18n?.locale || 'en',
      path: this.$route?.path || '/terms',
      robots: 'index, follow, max-snippet:160'
    })
  }
}
</script>

<style scoped>
.terms-page {
  min-height: 100vh;
  background: var(--bg-primary);
  color: var(--text-primary);
}

.terms-content {
  padding-top: 120px;
  padding-bottom: 4rem;
}

.content-wrapper {
  background: var(--bg-secondary);
  border-radius: 20px;
  padding: 3rem;
  box-shadow: 0 10px 40px var(--shadow-dark);
  border: 1px solid var(--border-color);
}

.page-title {
  font-size: 2.5rem;
  font-weight: 700;
  color: var(--text-primary);
  margin-bottom: 0.5rem;
  background: linear-gradient(135deg, var(--accent-primary), var(--accent-secondary));
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.last-updated {
  color: var(--text-muted);
  font-size: 0.9rem;
  margin-bottom: 2rem;
  padding-bottom: 1rem;
  border-bottom: 1px solid var(--border-color);
}

.terms-section {
  margin-bottom: 2.5rem;
}

.terms-section h2 {
  color: var(--text-primary);
  font-size: 1.5rem;
  font-weight: 600;
  margin-bottom: 1rem;
  padding-bottom: 0.5rem;
  border-bottom: 2px solid var(--accent-primary);
}

.terms-section p {
  color: var(--text-secondary);
  line-height: 1.7;
  margin-bottom: 1rem;
}

.terms-section ul {
  color: var(--text-secondary);
  line-height: 1.7;
  padding-left: 1.5rem;
}

.terms-section li {
  margin-bottom: 0.5rem;
}

.terms-section strong {
  color: var(--text-primary);
  font-weight: 600;
}

.back-link {
  text-align: center;
  margin-top: 3rem;
  padding-top: 2rem;
  border-top: 1px solid var(--border-color);
}

/* Responsive Design */
@media (max-width: 768px) {
  .terms-content {
    padding-top: 100px;
    padding-bottom: 2rem;
  }
  
  .content-wrapper {
    padding: 2rem 1.5rem;
    margin: 0 1rem;
  }
  
  .page-title {
    font-size: 2rem;
  }
}
</style>
