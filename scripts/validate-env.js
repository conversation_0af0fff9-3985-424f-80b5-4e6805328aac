#!/usr/bin/env node

/**
 * Environment Variables Validation Script
 * This script validates that all required environment variables are set correctly
 */

const path = require('path')
const fs = require('fs')

console.log('🔍 Validating environment variables...')

// Required environment variables for different deployment environments
const requiredVars = {
  development: [
    'NODE_ENV'
  ],
  production: [
    'NODE_ENV',
    'NUXT_ENV_BASE_URL',
    'NUXT_ENV_GA_ID'
  ],
  cloudflare: [
    'NODE_ENV',
    'NUXT_ENV_BASE_URL',
    'NUXT_ENV_GA_ID',
    'DEPLOY_ENV'
  ]
}

// Default values for environment variables
const defaultValues = {
  NODE_ENV: 'development',
  NUXT_ENV_BASE_URL: 'https://yugiohcardmaker.org',
  NUXT_ENV_GA_ID: 'G-PN1XZ4X9VG',
  DEPLOY_ENV: 'development'
}

/**
 * Load environment variables from .env files
 */
function loadEnvFile(filePath) {
  if (fs.existsSync(filePath)) {
    const content = fs.readFileSync(filePath, 'utf8')
    const lines = content.split('\n')
    
    lines.forEach(line => {
      const trimmed = line.trim()
      if (trimmed && !trimmed.startsWith('#')) {
        const [key, ...valueParts] = trimmed.split('=')
        const value = valueParts.join('=')
        if (key && value && !process.env[key]) {
          process.env[key] = value
        }
      }
    })
    
    console.log(`✅ Loaded environment variables from ${filePath}`)
  }
}

/**
 * Validate environment variables
 */
function validateEnvironment() {
  const deployEnv = process.env.DEPLOY_ENV || 'development'
  const nodeEnv = process.env.NODE_ENV || 'development'
  
  console.log(`📋 Current environment: ${nodeEnv}`)
  console.log(`📋 Deploy environment: ${deployEnv}`)
  
  // Determine which variables to check
  let varsToCheck = requiredVars.development
  
  if (deployEnv === 'CLOUDFLARE_PAGES') {
    varsToCheck = requiredVars.cloudflare
  } else if (nodeEnv === 'production') {
    varsToCheck = requiredVars.production
  }
  
  const missingVars = []
  const presentVars = []
  
  varsToCheck.forEach(varName => {
    const value = process.env[varName]
    if (!value) {
      missingVars.push(varName)
    } else {
      presentVars.push({ name: varName, value })
    }
  })
  
  // Report present variables
  if (presentVars.length > 0) {
    console.log('\n✅ Present environment variables:')
    presentVars.forEach(({ name, value }) => {
      // Mask sensitive values
      const displayValue = name.includes('GA_ID') ? value : 
                          name.includes('URL') ? value :
                          value.length > 20 ? `${value.substring(0, 20)}...` : value
      console.log(`   ${name}=${displayValue}`)
    })
  }
  
  // Report missing variables and provide defaults
  if (missingVars.length > 0) {
    console.log('\n⚠️  Missing environment variables:')
    missingVars.forEach(varName => {
      const defaultValue = defaultValues[varName]
      console.log(`   ${varName} (default: ${defaultValue})`)
      
      // Set default value
      if (defaultValue) {
        process.env[varName] = defaultValue
      }
    })
  }
  
  return missingVars.length === 0
}

/**
 * Generate environment file for Cloudflare Pages
 */
function generateCloudflareEnv() {
  const envContent = `# Generated environment variables for Cloudflare Pages
NODE_ENV=${process.env.NODE_ENV || 'production'}
NUXT_ENV_BASE_URL=${process.env.NUXT_ENV_BASE_URL || 'https://yugiohcardmaker.org'}
NUXT_ENV_GA_ID=${process.env.NUXT_ENV_GA_ID || 'G-PN1XZ4X9VG'}
DEPLOY_ENV=CLOUDFLARE_PAGES
NODE_OPTIONS=--openssl-legacy-provider --max-old-space-size=4096
`
  
  fs.writeFileSync('.env.production', envContent)
  console.log('✅ Generated .env.production for Cloudflare Pages')
}

/**
 * Main validation function
 */
function main() {
  try {
    // Load environment files
    loadEnvFile('.env')
    loadEnvFile('.env.local')
    loadEnvFile('.env.cloudflare')
    
    // Validate environment
    const isValid = validateEnvironment()
    
    // Generate Cloudflare environment file if needed
    if (process.env.DEPLOY_ENV === 'CLOUDFLARE_PAGES') {
      generateCloudflareEnv()
    }
    
    if (isValid) {
      console.log('\n🎉 Environment validation completed successfully!')
    } else {
      console.log('\n⚠️  Environment validation completed with warnings (using defaults)')
    }
    
  } catch (error) {
    console.error('❌ Environment validation failed:', error.message)
    process.exit(1)
  }
}

// Run validation
main()
