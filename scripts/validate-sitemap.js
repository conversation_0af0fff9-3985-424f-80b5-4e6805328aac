/**
 * Sitemap Validation Tool for Yu-Gi-Oh! Card Maker
 * Validates sitemap.xml structure and content
 */

const fs = require('fs')
const path = require('path')
const https = require('https')
const { DOMParser } = require('xmldom')

// Configuration
const config = {
  sitemapPath: path.join(__dirname, '../dist/sitemap.xml'),
  robotsPath: path.join(__dirname, '../static/robots.txt'),
  hostname: 'https://yugiohcardmaker.org'
}

/**
 * Validate XML structure
 */
function validateXMLStructure(xmlContent) {
  const errors = []
  
  try {
    const parser = new DOMParser({
      errorHandler: {
        warning: (msg) => errors.push(`Warning: ${msg}`),
        error: (msg) => errors.push(`Error: ${msg}`),
        fatalError: (msg) => errors.push(`Fatal Error: ${msg}`)
      }
    })
    
    const doc = parser.parseFromString(xmlContent, 'text/xml')
    
    if (errors.length > 0) {
      return { valid: false, errors }
    }
    
    // Check root element
    const urlset = doc.getElementsByTagName('urlset')[0]
    if (!urlset) {
      return { valid: false, errors: ['Missing urlset root element'] }
    }
    
    // Check namespace
    const xmlns = urlset.getAttribute('xmlns')
    if (xmlns !== 'http://www.sitemaps.org/schemas/sitemap/0.9') {
      errors.push('Invalid or missing sitemap namespace')
    }
    
    return { valid: errors.length === 0, errors }
    
  } catch (error) {
    return { valid: false, errors: [`XML parsing error: ${error.message}`] }
  }
}

/**
 * Validate sitemap content
 */
function validateSitemapContent(xmlContent) {
  const issues = []
  const stats = {
    totalUrls: 0,
    languages: new Set(),
    priorities: {},
    changefreqs: {}
  }
  
  try {
    const parser = new DOMParser()
    const doc = parser.parseFromString(xmlContent, 'text/xml')
    const urls = doc.getElementsByTagName('url')
    
    stats.totalUrls = urls.length
    
    for (let i = 0; i < urls.length; i++) {
      const url = urls[i]
      const loc = url.getElementsByTagName('loc')[0]
      const lastmod = url.getElementsByTagName('lastmod')[0]
      const changefreq = url.getElementsByTagName('changefreq')[0]
      const priority = url.getElementsByTagName('priority')[0]
      const alternates = url.getElementsByTagName('xhtml:link')
      
      // Validate required elements
      if (!loc) {
        issues.push(`URL ${i + 1}: Missing <loc> element`)
        continue
      }
      
      const urlValue = loc.textContent
      
      // Validate URL format
      if (!urlValue.startsWith(config.hostname)) {
        issues.push(`URL ${i + 1}: Invalid hostname in ${urlValue}`)
      }
      
      // Validate lastmod format
      if (lastmod) {
        const dateValue = lastmod.textContent
        if (isNaN(Date.parse(dateValue))) {
          issues.push(`URL ${i + 1}: Invalid lastmod date format: ${dateValue}`)
        }
      }
      
      // Validate changefreq
      if (changefreq) {
        const freq = changefreq.textContent
        const validFreqs = ['always', 'hourly', 'daily', 'weekly', 'monthly', 'yearly', 'never']
        if (!validFreqs.includes(freq)) {
          issues.push(`URL ${i + 1}: Invalid changefreq: ${freq}`)
        }
        stats.changefreqs[freq] = (stats.changefreqs[freq] || 0) + 1
      }
      
      // Validate priority
      if (priority) {
        const priorityValue = parseFloat(priority.textContent)
        if (isNaN(priorityValue) || priorityValue < 0 || priorityValue > 1) {
          issues.push(`URL ${i + 1}: Invalid priority: ${priority.textContent}`)
        }
        const priorityKey = priority.textContent
        stats.priorities[priorityKey] = (stats.priorities[priorityKey] || 0) + 1
      }
      
      // Check alternate language links
      for (let j = 0; j < alternates.length; j++) {
        const alternate = alternates[j]
        const hreflang = alternate.getAttribute('hreflang')
        const href = alternate.getAttribute('href')
        
        if (hreflang && hreflang !== 'x-default') {
          stats.languages.add(hreflang)
        }
        
        if (!href || !href.startsWith(config.hostname)) {
          issues.push(`URL ${i + 1}: Invalid alternate link href: ${href}`)
        }
      }
    }
    
    return { issues, stats }
    
  } catch (error) {
    return { issues: [`Content validation error: ${error.message}`], stats }
  }
}

/**
 * Validate robots.txt
 */
function validateRobotsTxt() {
  const issues = []
  
  try {
    if (!fs.existsSync(config.robotsPath)) {
      return { issues: ['robots.txt file not found'] }
    }
    
    const robotsContent = fs.readFileSync(config.robotsPath, 'utf8')
    
    // Check for sitemap reference
    if (!robotsContent.includes('Sitemap:')) {
      issues.push('robots.txt missing Sitemap directive')
    }
    
    // Check for correct sitemap URL
    if (!robotsContent.includes(`${config.hostname}/sitemap.xml`)) {
      issues.push('robots.txt missing correct sitemap URL')
    }
    
    // Check for basic structure
    if (!robotsContent.includes('User-agent:')) {
      issues.push('robots.txt missing User-agent directive')
    }
    
    return { issues }
    
  } catch (error) {
    return { issues: [`robots.txt validation error: ${error.message}`] }
  }
}

/**
 * Main validation function
 */
function validateSitemap() {
  console.log('🔍 Validating Sitemap...\n')
  
  // Check if sitemap exists
  if (!fs.existsSync(config.sitemapPath)) {
    console.error('❌ Sitemap file not found:', config.sitemapPath)
    return false
  }
  
  // Read sitemap content
  const xmlContent = fs.readFileSync(config.sitemapPath, 'utf8')
  
  // Validate XML structure
  console.log('📋 Validating XML structure...')
  const xmlValidation = validateXMLStructure(xmlContent)
  if (!xmlValidation.valid) {
    console.error('❌ XML Structure Validation Failed:')
    xmlValidation.errors.forEach(error => console.error(`   ${error}`))
    return false
  }
  console.log('✅ XML structure is valid')
  
  // Validate content
  console.log('\n📊 Validating sitemap content...')
  const contentValidation = validateSitemapContent(xmlContent)
  
  if (contentValidation.issues.length > 0) {
    console.error('⚠️  Content Issues Found:')
    contentValidation.issues.forEach(issue => console.error(`   ${issue}`))
  } else {
    console.log('✅ Sitemap content is valid')
  }
  
  // Display statistics
  console.log('\n📈 Sitemap Statistics:')
  console.log(`   Total URLs: ${contentValidation.stats.totalUrls}`)
  console.log(`   Languages: ${Array.from(contentValidation.stats.languages).join(', ')}`)
  console.log(`   Priorities: ${JSON.stringify(contentValidation.stats.priorities)}`)
  console.log(`   Change Frequencies: ${JSON.stringify(contentValidation.stats.changefreqs)}`)
  
  // Validate robots.txt
  console.log('\n🤖 Validating robots.txt...')
  const robotsValidation = validateRobotsTxt()
  if (robotsValidation.issues.length > 0) {
    console.error('⚠️  robots.txt Issues:')
    robotsValidation.issues.forEach(issue => console.error(`   ${issue}`))
  } else {
    console.log('✅ robots.txt is valid')
  }
  
  // Final result
  const hasErrors = !xmlValidation.valid || contentValidation.issues.length > 0 || robotsValidation.issues.length > 0
  
  console.log('\n' + '='.repeat(50))
  if (hasErrors) {
    console.log('⚠️  Validation completed with issues')
    return false
  } else {
    console.log('🎉 All validations passed successfully!')
    return true
  }
}

// Run validation if called directly
if (require.main === module) {
  const success = validateSitemap()
  process.exit(success ? 0 : 1)
}

module.exports = { validateSitemap }
