#!/bin/bash

# Cloudflare Pages Build Script
# This script ensures clean dependency installation and build for Cloudflare Pages

set -e  # Exit on any error

echo "🚀 Starting Cloudflare Pages build process..."

# Print environment info
echo "📋 Environment Information:"
echo "Node.js version: $(node --version)"
echo "npm version: $(npm --version)"
echo "Working directory: $(pwd)"

# Clean any existing node_modules and lock files conflicts
echo "🧹 Cleaning up existing dependencies..."
rm -rf node_modules
rm -f pnpm-lock.yaml  # Remove pnpm lock file if exists
rm -f yarn.lock       # Remove yarn lock file if exists

# Ensure we're using npm
echo "📦 Installing dependencies with npm..."
npm ci --prefer-offline --no-audit

# Verify critical dependencies
echo "🔍 Verifying critical dependencies..."
if ! npm list nuxt > /dev/null 2>&1; then
    echo "❌ Nuxt.js not found, installing..."
    npm install nuxt@^2.15.7
fi

if ! npm list cross-env > /dev/null 2>&1; then
    echo "❌ cross-env not found, installing..."
    npm install cross-env@^7.0.3
fi

# Run the build
echo "🏗️ Building application..."
npm run build:cloudflare

echo "✅ Build completed successfully!"

# Verify build output
if [ -d "dist" ]; then
    echo "📁 Build output directory exists"
    echo "📊 Build statistics:"
    echo "   HTML files: $(find dist -name "*.html" | wc -l)"
    echo "   JS files: $(find dist -name "*.js" | wc -l)"
    echo "   CSS files: $(find dist -name "*.css" | wc -l)"
    echo "   Total files: $(find dist -type f | wc -l)"
else
    echo "❌ Build output directory not found!"
    exit 1
fi

echo "🎉 Cloudflare Pages build completed successfully!"
