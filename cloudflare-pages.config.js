/**
 * Cloudflare Pages specific configuration
 * This file contains optimizations specifically for Cloudflare Pages deployment
 */

module.exports = {
  // Asset optimization settings
  assets: {
    // Cache control settings for different asset types
    cacheControl: {
      fonts: 'public, max-age=31536000, immutable',
      images: 'public, max-age=31536000, immutable',
      scripts: 'public, max-age=31536000, immutable',
      styles: 'public, max-age=31536000, immutable',
      data: 'public, max-age=86400',
      html: 'public, max-age=3600'
    },
    
    // Asset compression settings
    compression: {
      enabled: true,
      types: ['text/html', 'text/css', 'application/javascript', 'application/json', 'text/xml']
    }
  },

  // Security headers for Cloudflare Pages
  security: {
    headers: {
      'X-Frame-Options': 'DENY',
      'X-Content-Type-Options': 'nosniff',
      'Referrer-Policy': 'strict-origin-when-cross-origin',
      'Permissions-Policy': 'camera=(), microphone=(), geolocation=()',
      'X-XSS-Protection': '1; mode=block'
    }
  },

  // Performance optimizations
  performance: {
    // Enable HTTP/2 Server Push for critical resources
    http2Push: [
      '/_nuxt/runtime.js',
      '/fonts/font-face.css'
    ],
    
    // Preload critical resources
    preload: [
      { href: '/fonts/font-face.css', as: 'style' },
      { href: '/images/background.jpg', as: 'image' }
    ],
    
    // DNS prefetch for external resources
    dnsPrefetch: [
      'https://fonts.googleapis.com',
      'https://fonts.gstatic.com',
      'https://www.googletagmanager.com'
    ]
  },

  // Internationalization settings for Cloudflare Pages
  i18n: {
    defaultLocale: 'en',
    locales: ['en', 'zh', 'ja', 'de', 'fr', 'ko', 'pt', 'es', 'el', 'th', 'ru', 'vi'],
    strategy: 'prefix_except_default',
    baseUrl: 'https://yugiohcardmaker.org'
  },

  // Build optimization settings
  build: {
    // Chunk splitting strategy for better caching
    splitChunks: {
      vendor: true,
      runtime: true,
      commons: true
    },
    
    // Asset optimization
    optimization: {
      images: {
        formats: ['webp', 'jpg', 'png'],
        quality: 85
      },
      
      css: {
        minify: true,
        extractCritical: true
      },
      
      js: {
        minify: true,
        treeshake: true
      }
    }
  },

  // Environment-specific settings
  environments: {
    production: {
      baseUrl: 'https://yugiohcardmaker.org',
      analytics: {
        googleAnalytics: 'G-PN1XZ4X9VG'
      }
    },
    
    preview: {
      baseUrl: 'https://yugioh-card-maker-preview.pages.dev',
      analytics: {
        googleAnalytics: 'G-PN1XZ4X9VG'
      }
    }
  }
}
