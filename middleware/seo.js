/**
 * SEO Middleware for Yu-Gi-Oh! Card Maker
 * Ensures all pages have proper SEO configuration
 */

export default function ({ route, $config }) {
  // Only run on client-side
  if (process.client) {
    // Get base URL for structured data
    const baseUrl = $config?.baseURL || 'https://yugiohcardmaker.org'
    
    // Ensure Google Analytics is loaded
    if (typeof window !== 'undefined' && !window.gtag) {
      console.warn('Google Analytics not loaded properly')
    }
    
    // Add structured data if not present
    if (typeof window !== 'undefined' && !document.querySelector('script[type="application/ld+json"]')) {
      const structuredData = {
        '@context': 'https://schema.org',
        '@type': 'WebApplication',
        name: 'Yu-<PERSON><PERSON>-<PERSON>! Card Maker',
        alternateName: 'Yu-Gi-Oh! Card Maker',
        description: 'Free online Yu-Gi-Oh! card maker. Create custom monster, spell, and trap cards with professional quality.',
        url: `${baseUrl}/`,
        applicationCategory: 'DesignApplication',
        operatingSystem: 'Web Browser',
        offers: {
          '@type': 'Offer',
          price: '0',
          priceCurrency: 'USD'
        },
        author: {
          '@type': 'Organization',
          name: 'yugiohcardmaker.org',
          url: baseUrl
        },
        inLanguage: ['en', 'zh', 'ja', 'de', 'fr', 'ko', 'pt', 'es', 'el', 'th', 'ru', 'vi'],
        isAccessibleForFree: true,
        screenshot: `${baseUrl}/images/screenshot.jpg`
      }
      
      const script = document.createElement('script')
      script.type = 'application/ld+json'
      script.innerHTML = JSON.stringify(structuredData)
      document.head.appendChild(script)
    }
    
    // Canonical URL is now handled by page components via head() method
    
    // Ensure robots meta tag is present
    if (typeof window !== 'undefined') {
      const existingRobots = document.querySelector('meta[name="robots"]')
      if (!existingRobots) {
        const robots = document.createElement('meta')
        robots.name = 'robots'
        
        // Set appropriate robots directive based on route
        if (route.path.includes('/analytics-test')) {
          robots.content = 'noindex, nofollow'
        } else {
          robots.content = 'index, follow'
        }
        
        document.head.appendChild(robots)
      }
    }
    
    // Hreflang links are now handled by page components via head() method
  }
}
