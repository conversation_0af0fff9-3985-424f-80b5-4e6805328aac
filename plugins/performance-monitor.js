/**
 * 性能监控插件
 * 监控页面加载性能并提供优化建议
 */

export default ({ app }, inject) => {
  // 性能监控对象
  const performanceMonitor = {
    // 记录性能指标
    metrics: {
      navigationStart: 0,
      domContentLoaded: 0,
      loadComplete: 0,
      firstPaint: 0,
      firstContentfulPaint: 0,
      largestContentfulPaint: 0,
      firstInputDelay: 0,
      cumulativeLayoutShift: 0
    },

    // 初始化性能监控
    init() {
      if (process.client) {
        this.measureNavigationTiming()
        this.measurePaintTiming()
        this.measureLCP()
        this.measureFID()
        this.measureCLS()
        this.setupResourceObserver()
      }
    },

    // 测量导航时间
    measureNavigationTiming() {
      if (window.performance && window.performance.timing) {
        const timing = window.performance.timing
        this.metrics.navigationStart = timing.navigationStart
        this.metrics.domContentLoaded = timing.domContentLoadedEventEnd - timing.navigationStart
        this.metrics.loadComplete = timing.loadEventEnd - timing.navigationStart
      }
    },

    // 测量绘制时间
    measurePaintTiming() {
      if (window.performance && window.performance.getEntriesByType) {
        const paintEntries = window.performance.getEntriesByType('paint')
        paintEntries.forEach(entry => {
          if (entry.name === 'first-paint') {
            this.metrics.firstPaint = entry.startTime
          } else if (entry.name === 'first-contentful-paint') {
            this.metrics.firstContentfulPaint = entry.startTime
          }
        })
      }
    },

    // 测量最大内容绘制 (LCP)
    measureLCP() {
      if ('PerformanceObserver' in window) {
        try {
          const observer = new PerformanceObserver((list) => {
            const entries = list.getEntries()
            const lastEntry = entries[entries.length - 1]
            this.metrics.largestContentfulPaint = lastEntry.startTime
          })
          observer.observe({ entryTypes: ['largest-contentful-paint'] })
        } catch (e) {
          console.warn('LCP measurement not supported')
        }
      }
    },

    // 测量首次输入延迟 (FID)
    measureFID() {
      if ('PerformanceObserver' in window) {
        try {
          const observer = new PerformanceObserver((list) => {
            const entries = list.getEntries()
            entries.forEach(entry => {
              this.metrics.firstInputDelay = entry.processingStart - entry.startTime
            })
          })
          observer.observe({ entryTypes: ['first-input'] })
        } catch (e) {
          console.warn('FID measurement not supported')
        }
      }
    },

    // 测量累积布局偏移 (CLS)
    measureCLS() {
      if ('PerformanceObserver' in window) {
        try {
          let clsValue = 0
          const observer = new PerformanceObserver((list) => {
            const entries = list.getEntries()
            entries.forEach(entry => {
              if (!entry.hadRecentInput) {
                clsValue += entry.value
                this.metrics.cumulativeLayoutShift = clsValue
              }
            })
          })
          observer.observe({ entryTypes: ['layout-shift'] })
        } catch (e) {
          console.warn('CLS measurement not supported')
        }
      }
    },

    // 监控资源加载
    setupResourceObserver() {
      if ('PerformanceObserver' in window) {
        try {
          const observer = new PerformanceObserver((list) => {
            const entries = list.getEntries()
            entries.forEach(entry => {
              // 记录大文件加载时间
              if (entry.transferSize > 100000) { // 100KB以上
                console.warn(`Large resource detected: ${entry.name} (${Math.round(entry.transferSize / 1024)}KB, ${Math.round(entry.duration)}ms)`)
              }
            })
          })
          observer.observe({ entryTypes: ['resource'] })
        } catch (e) {
          console.warn('Resource monitoring not supported')
        }
      }
    },

    // 获取性能报告
    getReport() {
      return {
        ...this.metrics,
        recommendations: this.getRecommendations()
      }
    },

    // 获取优化建议
    getRecommendations() {
      const recommendations = []

      if (this.metrics.firstContentfulPaint > 2500) {
        recommendations.push('首次内容绘制时间过长，建议优化关键渲染路径')
      }

      if (this.metrics.largestContentfulPaint > 4000) {
        recommendations.push('最大内容绘制时间过长，建议优化图片和字体加载')
      }

      if (this.metrics.firstInputDelay > 300) {
        recommendations.push('首次输入延迟过长，建议优化JavaScript执行')
      }

      if (this.metrics.cumulativeLayoutShift > 0.25) {
        recommendations.push('累积布局偏移过大，建议为图片和广告预留空间')
      }

      if (this.metrics.domContentLoaded > 3000) {
        recommendations.push('DOM加载时间过长，建议减少JavaScript和CSS阻塞')
      }

      return recommendations
    },

    // 发送性能数据到分析服务
    sendToAnalytics() {
      if (window.gtag && this.metrics.loadComplete > 0) {
        window.gtag('event', 'page_load_performance', {
          custom_parameter_1: Math.round(this.metrics.firstContentfulPaint),
          custom_parameter_2: Math.round(this.metrics.largestContentfulPaint),
          custom_parameter_3: Math.round(this.metrics.domContentLoaded)
        })
      }
    }
  }

  // 注入到Vue实例
  inject('performance', performanceMonitor)

  // 页面加载完成后初始化
  if (process.client) {
    window.addEventListener('load', () => {
      setTimeout(() => {
        performanceMonitor.init()
        // 延迟发送分析数据
        setTimeout(() => {
          performanceMonitor.sendToAnalytics()
        }, 2000)
      }, 100)
    })
  }
}
