import uiLangData from '../static/lang.ui.json'
import cardMetaData from '../static/lang.card_meta.json'

export const state = () => ({
  _loadingDialogShow: false,
  _currentLanguage: 'en', // 默認語言改為英文
  _availableLanguages: [
    // 主要语言 - 优先显示
    {
      code: 'en',
      name: 'English',
      shortName: 'EN',
      flag: 'en',
      nativeName: 'English'
    },
    {
      code: 'ja',
      name: '日本語',
      shortName: '日本語',
      flag: 'ja',
      nativeName: '日本語'
    },
    {
      code: 'zh',
      name: '中文',
      shortName: '中文',
      flag: 'zh',
      nativeName: '中文'
    },
    // 其他语言 - 按字母顺序排列
    {
      code: 'de',
      name: 'Deutsch',
      shortName: 'DE',
      flag: 'de',
      nativeName: 'Deutsch'
    },
    {
      code: 'el',
      name: 'Ελληνικά',
      shortName: 'EL',
      flag: 'el',
      nativeName: 'Ελληνικά'
    },
    {
      code: 'es',
      name: 'Espa<PERSON><PERSON>',
      shortName: 'ES',
      flag: 'es',
      nativeName: 'Español'
    },
    {
      code: 'fr',
      name: 'Français',
      shortName: 'FR',
      flag: 'fr',
      nativeName: 'Français'
    },
    {
      code: 'ko',
      name: '한국어',
      shortName: '한국어',
      flag: 'ko',
      nativeName: '한국어'
    },
    {
      code: 'pt',
      name: 'Português',
      shortName: 'PT',
      flag: 'pt',
      nativeName: 'Português'
    },
    {
      code: 'ru',
      name: 'Русский',
      shortName: 'RU',
      flag: 'ru',
      nativeName: 'Русский'
    },
    {
      code: 'th',
      name: 'ไทย',
      shortName: 'TH',
      flag: 'th',
      nativeName: 'ไทย'
    },
    {
      code: 'vi',
      name: 'Tiếng Việt',
      shortName: 'VI',
      flag: 'vi',
      nativeName: 'Tiếng Việt'
    }
  ]
})

export const getters = {
  loadingDialogShow: (state) => state._loadingDialogShow,

  currentLanguage: (state) => state._currentLanguage,

  availableLanguages: (state) => state._availableLanguages,

  /**
   * 獲取當前語言的 UI 文字數據
   */
  currentUIData: (state) => {
    return uiLangData[state._currentLanguage] || uiLangData.en
  },

  /**
   * 獲取當前語言的卡片元數據
   */
  currentCardMeta: (state) => {
    return cardMetaData[state._currentLanguage] || cardMetaData.en
  },

  /**
   * 獲取指定語言的完整數據
   */
  getLanguageData: (state) => (langCode) => {
    const uiData = uiLangData[langCode] || uiLangData.en
    const cardData = cardMetaData[langCode] || cardMetaData.en

    return {
      ui: uiData,
      card: cardData,
      seo: uiData.seo || {}
    }
  },

  /**
   * 獲取當前語言的 SEO 數據
   */
  currentSEOData: (state, getters) => {
    const uiData = getters.currentUIData
    return uiData.seo || {
      title: '遊戲王卡片製造機',
      description: '免費的在線遊戲王卡片製作工具',
      keywords: '遊戲王,卡片,製作,工具'
    }
  }
}

export const actions = {
  /**
   * 初始化語言設置
   */
  initializeLanguage({ commit, state }) {
    // 從瀏覽器語言或 localStorage 獲取首選語言
    if (process.client) {
      const savedLang = localStorage.getItem('preferred-language')
      const browserLang = navigator.language.split('-')[0]

      let preferredLang = savedLang || browserLang || 'en'

      // 確保語言代碼有效
      const validLangs = state._availableLanguages.map(lang => lang.code)
      if (!validLangs.includes(preferredLang)) {
        preferredLang = 'en'
      }

      commit('setLanguage', preferredLang)
    }
  }
}

export const mutations = {
  fireLoadingDialog(state) {
    state._loadingDialogShow = true
  },

  closeLoadingDialog(state) {
    state._loadingDialogShow = false
  },

  /**
   * 設置當前語言
   */
  setLanguage(state, langCode) {
    const validLangs = state._availableLanguages.map(lang => lang.code)
    if (validLangs.includes(langCode)) {
      state._currentLanguage = langCode
    }
  }
}

