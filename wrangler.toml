# Cloudflare Pages Configuration for Yu-Gi-Oh! Card Maker
name = "yugioh-card-maker"
compatibility_date = "2024-01-01"

# Pages configuration
[env.production]
name = "yugioh-card-maker"

# Build configuration
[build]
command = "npm run build:cloudflare-simple"
cwd = "."
watch_dir = "."

# Environment variables for production
[env.production.vars]
NODE_ENV = "production"
NUXT_ENV_BASE_URL = "https://yugiohcardmaker.org"
NUXT_ENV_GA_ID = "G-PN1XZ4X9VG"

# Development environment
[env.preview]
name = "yugioh-card-maker-preview"

[env.preview.vars]
NODE_ENV = "development"
NUXT_ENV_BASE_URL = "https://yugioh-card-maker-preview.pages.dev"
NUXT_ENV_GA_ID = "G-PN1XZ4X9VG"

# Custom headers for static assets
[[headers]]
for = "/fonts/*"
[headers.values]
Cache-Control = "public, max-age=31536000, immutable"

[[headers]]
for = "/images/*"
[headers.values]
Cache-Control = "public, max-age=31536000, immutable"

[[headers]]
for = "/_nuxt/*"
[headers.values]
Cache-Control = "public, max-age=31536000, immutable"

[[headers]]
for = "/css/*"
[headers.values]
Cache-Control = "public, max-age=31536000, immutable"

[[headers]]
for = "/ygo/*"
[headers.values]
Cache-Control = "public, max-age=86400"

# Security headers
[[headers]]
for = "/*"
[headers.values]
X-Frame-Options = "DENY"
X-Content-Type-Options = "nosniff"
Referrer-Policy = "strict-origin-when-cross-origin"
Permissions-Policy = "camera=(), microphone=(), geolocation=()"
X-Robots-Tag = "index, follow"

# Sitemap headers
[[headers]]
for = "/*.xml"
[headers.values]
Content-Type = "text/xml; charset=utf-8"
Cache-Control = "public, max-age=3600"
X-Robots-Tag = "noindex"

# Main pages - enhanced SEO headers
[[headers]]
for = "/"
[headers.values]
X-Robots-Tag = "index, follow, max-snippet:-1, max-image-preview:large, max-video-preview:-1"

[[headers]]
for = "/*/index.html"
[headers.values]
X-Robots-Tag = "index, follow, max-snippet:-1, max-image-preview:large, max-video-preview:-1"

# Privacy and Terms pages
[[headers]]
for = "/privacy*"
[headers.values]
X-Robots-Tag = "index, follow, max-snippet:160"

[[headers]]
for = "/terms*"
[headers.values]
X-Robots-Tag = "index, follow, max-snippet:160"

# Analytics test page - no indexing
[[headers]]
for = "/analytics-test*"
[headers.values]
X-Robots-Tag = "noindex, nofollow"

# Robots.txt
[[headers]]
for = "/robots.txt"
[headers.values]
Content-Type = "text/plain; charset=utf-8"
Cache-Control = "public, max-age=86400"
X-Robots-Tag = "noindex"
